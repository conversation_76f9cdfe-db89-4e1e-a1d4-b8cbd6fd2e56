<?php

$content = file_get_contents('api.json');
$data = json_decode($content, true);
$acceptancesGet = $data['paths']['/internal/acceptances']['get'];
echo 'Parameters: ' . (isset($acceptancesGet['parameters']) ? count($acceptancesGet['parameters']) : 0) . PHP_EOL;
if (isset($acceptancesGet['parameters'])) {
    foreach ($acceptancesGet['parameters'] as $param) {
        echo '- ' . $param['name'] . ' (' . $param['schema']['type'] . ')' . PHP_EOL;
    }
} else {
    echo 'No parameters found' . PHP_EOL;
}
